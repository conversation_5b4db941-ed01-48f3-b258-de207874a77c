# 股票看图软件新功能说明

## 概述

本次更新为股票看图软件增加了两个重要功能：

1. **策略指标选股功能** - 在自定义策略面板中添加了基于技术指标的股票筛选功能
2. **优化多股票回测进度条** - 改进了多股票回测的进度显示，提供详细的百分比进度和状态信息

## 功能一：策略指标选股功能

### 功能位置
- 位于 **"自定义策略"** 选项卡
- 点击 **"指标选股"** 按钮打开选股窗口

### 主要特性

#### 1. 股票池选择
- **沪深300**: 选择沪深300指数成分股（约300只大盘蓝筹股）
- **中证1000**: 选择中证1000指数成分股（约1000只中小盘股）
- **上证50**: 选择上证50指数成分股（约50只超大盘股）
- **全部A股**: 选择A股市场所有股票（全市场数千只股票）
- **主板**: 选择主板上市股票
- **创业板**: 选择创业板股票
- **科创板**: 选择科创板股票
- **北交所**: 选择北交所股票
- **自定义列表**: 使用多股票回测中的股票列表

> **关于全市场选股**: "全部A股"选项会对A股市场的所有股票进行筛选，包括主板、创业板、科创板等所有板块的股票。如果市场数据管理器可用，会获取完整的股票列表；否则使用包含各板块代表性股票的示例列表。

#### 2. 策略选股支持
- **MACD策略**: 使用MACD金叉作为买点信号进行选股
- **KDJ策略**: 使用KDJ超卖区域金叉作为买点信号进行选股
- **自定义策略**: 使用用户编写的自定义策略代码的买点逻辑进行选股

> **重要说明**: 指标选股功能直接使用策略的买点设置，而不是单独的条件类型。这意味着选股结果就是在股票分析面板中会出现买点信号的股票。

#### 3. 策略参数设置
不同策略支持不同的参数设置：
- **MACD策略**: 快线周期(12)、慢线周期(26)、信号周期(9)
- **KDJ策略**: K周期(9)、超卖线(20)、超买线(80)
- **自定义策略**: 使用自定义策略页面编写的策略代码和参数

#### 4. 结果展示
- **排名**: 按信号强度排序
- **股票代码**: 显示股票代码
- **股票名称**: 显示股票名称
- **策略信号**: 显示策略买点信号信息
- **信号强度**: 显示信号强度评分（基于信号新鲜度）
- **最新价格**: 显示最新收盘价
- **涨跌幅**: 显示价格变化百分比
- **信号日期**: 显示买点信号出现的日期

#### 5. 功能操作
- **开始选股**: 执行策略买点选股分析
- **导出结果**: 支持导出为Excel、CSV或文本格式
- **应用到多股票回测**: 将选股结果直接导入多股票回测模块进行验证
- **选股结果**: 查看详细的选股报告和策略信号信息

### 实时选股功能

#### 实时数据支持
- **当天选股**: 默认选股日期设置为当天，支持获取当天的最新数据
- **实时按钮**: 点击"实时"按钮可快速设置为当前日期
- **数据来源**: 通过市场数据管理器获取最新的股票数据，包括当天的实时行情
- **缓存机制**: 支持数据缓存，提高重复查询的效率

#### 实时选股的优势
1. **及时发现机会**: 基于当天最新数据进行技术指标分析
2. **动态调整**: 可以根据盘中变化及时调整选股策略
3. **实时验证**: 对选股结果进行实时验证和跟踪

### 使用流程

1. **准备策略**（如使用自定义策略）：
   - 在"自定义策略"选项卡中编写策略代码
   - 确保策略代码包含买点逻辑（signal = 1）

2. **执行选股**：
   - 在"自定义策略"选项卡中点击"指标选股"
   - 选择股票池（如"全部A股"进行全市场选股）
   - 选择策略类型（MACD、KDJ或自定义策略）
   - 设置策略参数（使用默认值或自定义）
   - 设置选股日期（点击"实时"按钮使用当天日期）和数据回看天数
   - 点击"开始选股"执行筛选

3. **查看结果**：
   - 查看选股结果列表，按信号强度排序
   - 查看每只股票的策略信号信息和信号日期
   - 可导出结果或直接应用到多股票回测进行验证

### 选股范围说明

#### 全市场选股
- 选择"全部A股"可以对整个A股市场进行选股
- 包括主板、创业板、科创板、北交所等所有板块
- 股票数量通常在4000-5000只左右（根据市场情况变化）

#### 板块选股
- 可以选择特定板块进行精准选股
- 不同板块的股票特性和投资逻辑不同
- 建议根据投资策略选择合适的板块

## 功能二：优化多股票回测进度条

### 改进内容

#### 1. 详细进度显示
- **百分比进度**: 显示精确的完成百分比（如 45.2%）
- **当前状态**: 显示当前执行阶段（数据加载、回测进行中、结果计算等）
- **详细信息**: 显示具体的处理信息（如正在处理的股票代码）

#### 2. 分阶段进度跟踪
- **数据加载阶段** (0-40%): 显示股票数据加载进度
- **资金分配阶段** (40-45%): 显示资金分配计算
- **回测执行阶段** (45-95%): 显示各股票回测进度
- **结果计算阶段** (95-100%): 显示投资组合结果计算

#### 3. 实时状态更新
- 实时显示当前处理的股票代码
- 显示已完成/总数量的比例
- 显示每个阶段的完成状态

#### 4. 界面优化
- 进度条更加醒目，长度增加到400像素
- 添加了专门的百分比显示标签
- 状态信息和详细信息分别显示
- 使用不同颜色区分不同类型的信息

### 进度显示示例

```
回测进度: 67.3%
当前状态: 回测进行中 (67.3%)
详细信息: 完成股票 000002.SZ 回测 (134/200)
```

### 技术实现

#### 1. 进度回调机制
- 在多股票回测系统中添加了 `progress_callback` 参数
- 支持传递进度百分比、消息和阶段信息
- 使用线程安全的方式更新UI界面

#### 2. 分阶段进度计算
- 数据加载: 根据已加载股票数量计算进度
- 回测执行: 根据已完成回测的股票数量计算进度
- 结果计算: 显示最终的结果处理进度

## 使用建议

### 策略指标选股
1. **选择合适的股票池**: 根据投资策略选择相应的股票池
2. **参数优化**: 可以尝试不同的指标参数组合
3. **结合多个指标**: 可以多次选股，结合不同指标的结果
4. **回测验证**: 将选股结果导入多股票回测进行验证

### 多股票回测
1. **关注进度信息**: 通过进度条了解回测进展
2. **大规模回测**: 对于大量股票，系统会自动分批处理
3. **API限流**: 系统会自动处理API调用限制

## 注意事项

1. **数据依赖**: 选股功能依赖于市场数据管理器，确保数据源正常
2. **网络连接**: 大规模选股和回测需要稳定的网络连接
3. **计算时间**: 股票数量较多时，选股和回测可能需要较长时间
4. **内存使用**: 大规模数据处理时注意内存使用情况

## 技术特点

1. **多线程处理**: 支持并发数据获取和回测
2. **API限流**: 智能控制API调用频率
3. **缓存机制**: 利用数据缓存提高效率
4. **错误处理**: 完善的异常处理和错误提示
5. **用户体验**: 直观的进度显示和状态反馈

这两个新功能大大增强了软件的实用性，为用户提供了更强大的股票分析和回测工具。
