# 量化股票软件使用说明

## 软件概述

这是一个功能完整的量化股票分析软件，集成了用户注册登录、数据获取、技术分析、回测系统等功能。软件已经过优化，具有以下特点：

### 核心优化功能

1. **数据获取限制保护** - 每分钟API调用次数限制，保护账号安全
2. **多股票回测增强** - 支持板块股票一键加载和批量回测
3. **用户体验优化** - 登录成功后token后台保存，获取失败时友好提示
4. **板块分析功能** - 支持沪深300、中证1000等指数成分股分析
5. **市场数据管理** - 智能缓存和批量数据获取优化

## 安装和启动

### 方法1：使用打包好的exe文件

1. 下载软件包并解压
2. 双击 `启动程序.bat` 或直接运行 `量化股票软件.exe`
3. 首次使用需要注册Tushare账号

### 方法2：从源码运行

1. 安装Python 3.8+
2. 安装依赖：`pip install -r requirements.txt`
3. 运行：`python 登录注册.py`

## 功能详解

### 1. 用户注册登录

#### 注册新账号
- 输入手机号（密码自动设置为123456）
- 输入图片验证码
- 发送并输入手机验证码
- 系统自动填写用户信息并获取Token

#### 登录已有账号
- 输入手机号
- 输入图片验证码
- 系统自动登录并获取Token

**注意**：登录成功后Token会在后台保存，不会生成文件，保护用户隐私。

### 2. 股票数据获取

#### 数据获取优化
- **API限流保护**：每分钟最多450次调用，保护账号安全
- **智能缓存**：本地压缩缓存，减少重复请求
- **批量获取**：支持批量股票数据获取，提高效率
- **友好提示**：获取失败时显示"欢迎使用"而不是错误信息

#### 支持的数据类型
- 日线数据（开高低收、成交量等）
- 实时数据（当前价格、涨跌幅等）
- 股票基本信息（名称、行业、地区等）

### 3. 板块分析功能

#### 支持的板块类型
- **市场板块**：主板、中小板、创业板、科创板、北交所
- **指数板块**：沪深300、中证1000、上证50
- **自定义板块**：手动添加股票代码

#### 板块股票加载
1. 在多股票回测界面选择板块类型
2. 点击"加载板块股票"按钮
3. 系统自动获取该板块所有股票代码
4. 支持大规模回测（500+股票）智能提示

### 4. 回测系统

#### 单股票回测
- 支持多种技术指标策略
- 自定义策略代码编写
- 详细的回测报告和图表

#### 多股票回测
- **板块回测**：一键加载板块股票进行批量回测
- **性能优化**：支持并发处理和API限流
- **结果分析**：统计分析和排序功能
- **大规模支持**：支持500+股票同时回测

#### 策略类型
- 移动平均策略
- MACD策略
- KDJ策略
- RSI策略
- 自定义策略

### 5. 实时监控

- 多股票实时价格监控
- 技术指标实时计算
- 买卖信号提示
- 性能统计和分析

### 6. 网页交易（可选）

- 集成网页交易功能
- 自动化下单支持
- 持仓分析

## 使用技巧

### 1. 数据获取优化

**合理使用API**：
- 避免频繁刷新数据
- 优先使用缓存数据
- 大规模回测时选择较短时间范围

**网络优化**：
- 确保网络连接稳定
- 关闭防火墙或添加程序到白名单
- 避免在网络高峰期进行大规模数据获取

### 2. 多股票回测优化

**选择合适的股票池**：
- 小规模测试：选择50只以内股票
- 中等规模：选择100-300只股票
- 大规模回测：选择500+股票（需要更长时间）

**时间范围设置**：
- 短期回测：1-3个月
- 中期回测：6个月-1年
- 长期回测：1年以上

**策略优化**：
- 先在小规模股票池测试策略
- 确认策略有效后再扩大范围
- 注意策略的适用性和稳定性

### 3. 板块分析技巧

**板块选择**：
- **沪深300**：大盘蓝筹股，适合稳健策略
- **中证1000**：中小盘股，适合成长策略
- **创业板**：高成长性股票，波动较大
- **科创板**：科技创新股票，风险较高

**分析方法**：
- 对比不同板块的回测结果
- 分析板块轮动规律
- 结合市场环境选择合适板块

## 常见问题

### Q1：登录失败怎么办？
A1：
- 检查网络连接
- 确认手机号和验证码正确
- 尝试刷新验证码
- 检查是否被防火墙阻止

### Q2：数据获取很慢怎么办？
A2：
- 这是正常现象，API有限流保护
- 避免同时获取大量数据
- 优先使用缓存数据
- 选择网络较好的时间段

### Q3：回测结果不准确怎么办？
A3：
- 检查策略逻辑是否正确
- 确认数据质量
- 考虑交易成本和滑点
- 避免过度拟合

### Q4：软件运行出错怎么办？
A4：
- 检查Python环境和依赖
- 查看控制台错误信息
- 重启软件
- 联系技术支持

## 技术支持

如遇到问题，请：
1. 查看控制台输出的详细错误信息
2. 检查网络连接和防火墙设置
3. 确认系统环境（Windows 10/11）
4. 提供详细的错误描述和复现步骤

## 版本更新

当前版本：增强版 v2.0

主要更新：
- 优化数据获取限制保护
- 增强多股票回测功能
- 改进板块股票加载
- 优化用户体验
- 修复已知问题
