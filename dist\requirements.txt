# 量化股票软件依赖包列表

# 基础数据处理
pandas>=1.3.0
numpy>=1.21.0

# 股票数据接口
tushare>=1.2.89

# 图表绘制
matplotlib>=3.5.0

# GUI界面
# tkinter 是Python标准库，无需安装

# 网页自动化
selenium>=4.0.0
webdriver-manager>=3.8.0

# 网络请求
requests>=2.25.0
urllib3>=1.26.0

# 数据格式处理
openpyxl>=3.0.0
xlrd>=2.0.0
xlsxwriter>=3.0.0

# 网页解析
lxml>=4.6.0
beautifulsoup4>=4.9.0

# 图像处理
Pillow>=8.0.0

# 打包工具
pyinstaller>=5.0.0

# 系统工具
psutil>=5.8.0

# 数据压缩
# gzip 是Python标准库，无需安装

# 其他工具库
# json, os, sys, threading, time, datetime 等都是Python标准库

# 可选依赖（用于高级功能）
# scikit-learn>=1.0.0  # 机器学习（可选）
# scipy>=1.7.0         # 科学计算（可选）
# ta-lib>=0.4.0        # 技术分析库（可选，需要额外安装）
