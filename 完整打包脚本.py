#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票看图软件完整打包脚本
功能：
1. 打包成单个exe文件
2. 包含所有依赖和功能模块
3. 保持数据获取限制量
4. 支持多股票回测和板块加载
5. 登录成功后不生成token.txt文件
6. 获取股票列表失败时显示"欢迎使用"
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False

def create_enhanced_spec_file():
    """创建增强版的PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件和模块
added_files = [
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('多股票监控管理器.py', '.'),
    ('多股票监控配置.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('交易调度器.py', '.'),
    ('浏览器驱动管理.py', '.'),
    ('策略示例', '策略示例'),
    ('user_config', 'user_config'),
    ('market_data_cache', 'market_data_cache'),
]

a = Analysis(
    ['登录注册.py'],  # 使用登录注册作为主入口
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        # 基础库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.filedialog',
        
        # 数据处理
        'pandas',
        'numpy',
        'tushare',
        
        # 图表绘制
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        
        # 网页自动化
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.edge.options',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.support.expected_conditions',
        
        # 网络请求
        'requests',
        'urllib3',
        
        # 数据格式
        'openpyxl',
        'xlrd',
        'xlsxwriter',
        'lxml',
        'bs4',
        
        # 图像处理
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        
        # 系统和工具
        'datetime',
        'threading',
        'json',
        'time',
        'os',
        'sys',
        'subprocess',
        'importlib',
        'importlib.util',
        'base64',
        'io',
        'gzip',
        'pickle',
        'logging',
        'collections',
        'typing',
        'abc',
        'warnings',
        'queue',
        'concurrent.futures',
        
        # WebDriver管理器
        'webdriver_manager',
        'webdriver_manager.microsoft',
        'webdriver_manager.chrome',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的大型库
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
        'IPython',
        'jupyter',
        'notebook',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='量化股票软件',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('量化股票软件.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建增强版打包配置文件: 量化股票软件.spec")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "量化股票软件.spec"]
        
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 可执行文件构建成功")
            return True
        else:
            print("❌ 构建失败")
            print("错误输出:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def copy_additional_files():
    """复制额外需要的文件到dist目录"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 需要复制的文件列表
    files_to_copy = [
        "requirements.txt",
        "README.md",
        "使用说明.md",
        "新功能说明.md",
    ]
    
    print("正在复制额外文件...")
    
    # 复制文件
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, dist_dir)
                print(f"✓ 已复制: {file_name}")
            except Exception as e:
                print(f"⚠ 复制文件失败 {file_name}: {str(e)}")
    
    return True

def create_startup_script():
    """创建启动脚本"""
    startup_script = '''@echo off
echo 启动量化股票软件...
echo.
echo 正在检查系统环境...

REM 检查是否存在可执行文件
if not exist "量化股票软件.exe" (
    echo 错误: 未找到量化股票软件.exe
    pause
    exit /b 1
)

echo 启动程序...
start "" "量化股票软件.exe"

echo 程序已启动，请在弹出的窗口中操作
echo 如果程序没有启动，请检查防火墙设置
pause
'''
    
    with open('dist/启动程序.bat', 'w', encoding='gbk') as f:
        f.write(startup_script)
    
    print("✓ 已创建启动脚本: 启动程序.bat")

def create_readme():
    """创建README文件"""
    from datetime import datetime

    readme_content = f'''# 量化股票软件

## 软件介绍
这是一个功能完整的量化股票分析软件，集成了登录注册、数据获取、技术分析、回测系统等功能。

## 主要功能
1. **用户登录注册** - 自动化Tushare账号注册和登录
2. **股票数据获取** - 支持实时和历史数据获取，带API限流保护
3. **技术指标分析** - 内置多种技术指标计算
4. **单股票回测** - 支持自定义策略回测
5. **多股票回测** - 支持板块股票批量回测
6. **板块分析** - 支持沪深300、中证1000等板块股票加载
7. **实时监控** - 支持多股票实时数据监控
8. **网页交易** - 集成网页交易功能

## 使用方法

### 方法1：双击启动脚本
```
双击 "启动程序.bat" 文件
```

### 方法2：直接运行主程序
```
双击 "量化股票软件.exe" 文件
```

## 功能特点

### 数据获取优化
- 每分钟API调用限制保护
- 智能缓存机制
- 批量数据获取优化
- 获取失败时友好提示

### 多股票回测增强
- 支持板块股票一键加载
- 沪深300、中证1000、上证50等指数成分股
- 主板、创业板、科创板等板块分类
- 批量回测性能优化

### 用户体验优化
- 登录成功后token后台保存，不生成文件
- 获取股票列表失败时显示"欢迎使用"
- 界面友好，操作简单

## 注意事项
1. 首次使用需要注册Tushare账号
2. 确保网络连接正常
3. 建议关闭防火墙或添加程序到白名单
4. 数据获取受API限制，请合理使用

## 技术支持
如有问题，请检查：
1. 网络连接是否正常
2. 防火墙设置是否阻止程序运行
3. 系统是否支持（Windows 10及以上）

## 版本信息
- 版本: 增强版 v2.0
- 构建日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 支持系统: Windows 10/11
'''

    with open('dist/README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print("✓ 已创建README文件")

def cleanup_temp_files():
    """清理临时文件"""
    temp_files = [
        "量化股票软件.spec",
        "build",
        "__pycache__",
        "*.pyc",
    ]

    print("正在清理临时文件...")

    for pattern in temp_files:
        if pattern == "build" and os.path.exists("build"):
            try:
                shutil.rmtree("build")
                print("✓ 已删除build目录")
            except Exception as e:
                print(f"⚠ 删除build目录失败: {e}")
        elif pattern == "__pycache__":
            for root, dirs, files in os.walk("."):
                if "__pycache__" in dirs:
                    try:
                        shutil.rmtree(os.path.join(root, "__pycache__"))
                        print(f"✓ 已删除 {os.path.join(root, '__pycache__')}")
                    except Exception as e:
                        print(f"⚠ 删除__pycache__失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("量化股票软件 - 完整打包脚本")
    print("=" * 60)

    # 检查当前目录
    required_files = ['登录注册.py', '股票看图软件_增强版.py', '市场数据管理.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 未找到必需文件: {file}")
            print("请确保在正确的目录中运行此脚本")
            return False

    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False

    # 创建spec配置文件
    create_enhanced_spec_file()

    # 构建可执行文件
    if not build_executable():
        return False

    # 复制额外文件
    copy_additional_files()

    # 创建启动脚本
    create_startup_script()

    # 创建README
    create_readme()

    # 清理临时文件
    cleanup_temp_files()

    print("\n" + "=" * 60)
    print("✅ 打包完成！")
    print("=" * 60)
    print("📁 输出目录: dist/")
    print("🚀 主程序: 量化股票软件.exe")
    print("📋 启动脚本: 启动程序.bat")
    print("📖 说明文档: README.md")
    print("\n🎉 您可以将dist目录中的所有文件分发给用户使用！")

    return True

if __name__ == "__main__":
    main()
