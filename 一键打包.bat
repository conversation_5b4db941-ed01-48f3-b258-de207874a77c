@echo off
chcp 65001 >nul
echo ================================================================
echo                    量化股票软件一键打包工具
echo ================================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保已安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✓ Python环境检查通过
echo.

echo 正在检查必需文件...
if not exist "登录注册.py" (
    echo ❌ 错误: 未找到 登录注册.py
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

if not exist "股票看图软件_增强版.py" (
    echo ❌ 错误: 未找到 股票看图软件_增强版.py
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

if not exist "市场数据管理.py" (
    echo ❌ 错误: 未找到 市场数据管理.py
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo ✓ 必需文件检查通过
echo.

echo 开始执行打包脚本...
echo ----------------------------------------------------------------
python 完整打包脚本.py

if errorlevel 1 (
    echo.
    echo ❌ 打包过程中出现错误
    echo 请检查上面的错误信息
    pause
    exit /b 1
)

echo.
echo ================================================================
echo                        打包完成！
echo ================================================================
echo.
echo 📁 输出目录: dist\
echo 🚀 主程序: 量化股票软件.exe
echo 📋 启动脚本: 启动程序.bat
echo 📖 说明文档: README.md
echo.
echo 🎉 您可以将dist目录中的所有文件分发给用户使用！
echo.

REM 询问是否打开输出目录
set /p choice="是否打开输出目录？(Y/N): "
if /i "%choice%"=="Y" (
    if exist "dist" (
        explorer dist
    ) else (
        echo ❌ dist目录不存在
    )
)

echo.
echo 按任意键退出...
pause >nul
